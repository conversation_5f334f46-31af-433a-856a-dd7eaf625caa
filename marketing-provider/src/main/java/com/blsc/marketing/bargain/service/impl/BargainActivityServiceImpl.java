package com.blsc.marketing.bargain.service.impl;

import com.blsc.commons.id.IdWorkerCommon;
import com.blsc.marketing.bargain.bean.PageData;
import com.blsc.marketing.bargain.bean.bo.BargainAssistRecordBO;
import com.blsc.marketing.bargain.bean.bo.UserBargainActivityBO;
import com.blsc.marketing.bargain.bean.dto.AssistBargainDTO;
import com.blsc.marketing.bargain.bean.dto.QueryBargainActivityDTO;
import com.blsc.marketing.bargain.bean.dto.StartBargainActivityDTO;
import com.blsc.marketing.bargain.bean.po.BargainActivityConfigPO;
import com.blsc.marketing.bargain.bean.po.BargainAssistRecordPO;
import com.blsc.marketing.bargain.bean.po.UserBargainActivityPO;
import com.blsc.marketing.bargain.bean.po.UserDailyAssistLimitPO;
import com.blsc.marketing.bargain.mapper.BargainActivityConfigMapper;
import com.blsc.marketing.bargain.mapper.BargainAssistRecordMapper;
import com.blsc.marketing.bargain.mapper.UserBargainActivityMapper;
import com.blsc.marketing.bargain.mapper.UserDailyAssistLimitMapper;
import com.blsc.marketing.bargain.service.BargainActivityService;
import com.blsc.marketing.bargain.util.BargainAmountCalculatorV2;
import com.blsc.marketing.bargain.util.BargainBeanConverter;
import com.blsc.marketing.exception.BizException;
import com.blsc.marketing.exception.MarketingServiceCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 助力活动服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BargainActivityServiceImpl implements BargainActivityService {

    private static final int DAILY_ASSIST_LIMIT = 5; // 每日助力限制

    private final BargainActivityConfigMapper bargainActivityConfigMapper;

    private final UserBargainActivityMapper userBargainActivityMapper;

    private final BargainAssistRecordMapper bargainAssistRecordMapper;

    private final UserDailyAssistLimitMapper userDailyAssistLimitMapper;

    private final RedissonClient redissonClient;

    private final IdWorkerCommon idWorkerCommon;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserBargainActivityBO startBargainActivity(StartBargainActivityDTO dto) {
        log.info("用户发起助力活动: userId={}, activityId={}, productId={}",
                dto.getUserId(), dto.getActivityId(), dto.getProductId());

        try {
            // 1. 查询活动配置
            BargainActivityConfigPO config = bargainActivityConfigMapper.selectById(dto.getActivityId());
            if (config == null || config.getStatus() != 1) {
                throw new BizException(MarketingServiceCode.NOT_FIND, "活动配置不存在或已禁用");
            }

            // 2. 检查用户是否已经发起过该活动
            UserBargainActivityPO existing = userBargainActivityMapper.selectByUserAndActivity(
                    dto.getUserId(), dto.getActivityId());
            if (existing != null) {
                throw new BizException(MarketingServiceCode.FAILED_PRECONDITION, "用户已经发起过该活动");
            }

            // 3. 随机生成实际所需助力人数
            int requiredAssistCount = generateRandomAssistCount(config.getMinAssistCount(), config.getMaxAssistCount());

            // 4. 创建用户助力活动
            UserBargainActivityPO userBargain = new UserBargainActivityPO();
            userBargain.setId(idWorkerCommon.nextId());
            userBargain.setActivityId(dto.getActivityId());
            userBargain.setUserId(dto.getUserId());
//            userBargain.setAppId(dto.getAppId());
            userBargain.setProductId(dto.getProductId());
            userBargain.setOriginalPrice(config.getOriginalPrice());
            userBargain.setFloorPrice(config.getFloorPrice());
            userBargain.setCurrentPrice(config.getOriginalPrice());
            userBargain.setTotalBargainAmount(0);
            userBargain.setAssistCount(0);
            userBargain.setMinAssistCount(requiredAssistCount); // 存储随机生成的实际所需助力人数
            userBargain.setStatus(1); // 进行中
            userBargain.setExpireTime(LocalDateTime.now().plusHours(config.getActivityDurationHours()));
            userBargain.setCreateTime(LocalDateTime.now());
            userBargain.setUpdateTime(LocalDateTime.now());

            int result = userBargainActivityMapper.insertOne(userBargain);
            if (result <= 0) {
                throw new BizException(MarketingServiceCode.INTERNAL_ERROR, "创建助力活动失败");
            }

            log.info("助力活动创建成功: userBargainId={}, requiredAssistCount={}", userBargain.getId(), requiredAssistCount);
            return BargainBeanConverter.convertToUserBargainActivityBO(userBargain);

        } catch (BizException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("发起助力活动失败", e);
            throw new BizException(MarketingServiceCode.INTERNAL_ERROR, "发起助力活动失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BargainAssistRecordBO assistBargain(AssistBargainDTO dto) {
        log.info("用户参与助力: assistUserId={}, userBargainId={}",
                dto.getAssistUserId(), dto.getUserBargainId());

        try {
            // 1. 获取分布式锁，防止并发问题，对这个业务来说用乐观锁更优，但用分布式锁更方便
            acquireDistributedLock(dto.getUserBargainId());

            // 2. 在锁保护下检查权限和获取最新状态
            UserBargainActivityPO userBargain = validateAndGetBargainActivity(dto);

            // 3. 计算砍价金额
            int bargainAmount = calculateBargainAmount(userBargain);

            // 4. 创建助力记录
            BargainAssistRecordPO assistRecord = createAssistRecord(dto, userBargain, bargainAmount);

            // 5. 更新助力活动状态
            updateBargainActivityStatus(userBargain, bargainAmount);

            // 6. 更新用户每日助力次数
            updateUserDailyAssistCount(dto.getAssistUserId());

            log.info("助力成功: assistRecordId={}, bargainAmount={}, currentPrice={}",
                    assistRecord.getId(), bargainAmount, userBargain.getCurrentPrice());

            return BargainBeanConverter.convertToBargainAssistRecordBO(assistRecord);

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("参与助力失败", e);
            throw new BizException(MarketingServiceCode.INTERNAL_ERROR, "参与助力失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PageData<UserBargainActivityBO> queryUserBargainActivities(QueryBargainActivityDTO dto) {
        log.info("查询用户助力活动列表: userId={}, status={}", dto.getUserId(), dto.getStatus());

        try {
            int offset = (dto.getPageNum() - 1) * dto.getPageSize();
            List<UserBargainActivityPO> activities = userBargainActivityMapper.selectByUserWithPage(
                    dto.getUserId(), dto.getStatus(), offset, dto.getPageSize());

            int total = userBargainActivityMapper.countByUser(dto.getUserId(), dto.getStatus());

            List<UserBargainActivityBO> activityBOs = BargainBeanConverter.convertToUserBargainActivityBOList(activities);

            // 填充额外信息
            for (UserBargainActivityBO activityBO : activityBOs) {
                fillBargainActivityExtraInfo(activityBO);
            }

            PageData<UserBargainActivityBO> pageData = new PageData<>();
            pageData.setPageNum(dto.getPageNum()).setPageSize(dto.getPageSize()).setTotalSize((long) total).setList(activityBOs);

            return pageData;

        } catch (Exception e) {
            log.error("查询用户助力活动列表失败", e);
            throw new BizException(MarketingServiceCode.INTERNAL_ERROR, "查询助力活动列表失败: " + e.getMessage(), e);
        }
    }

    @Override
    public UserBargainActivityBO getBargainActivityById(Long userBargainId, boolean includeRecords) {
        log.info("查询助力活动详情: userBargainId={}, includeRecords={}", userBargainId, includeRecords);

        try {
            UserBargainActivityPO userBargain = userBargainActivityMapper.selectById(userBargainId);
            if (userBargain == null) {
                throw new BizException(MarketingServiceCode.NOT_FIND, "助力活动不存在");
            }

            UserBargainActivityBO activityBO = BargainBeanConverter.convertToUserBargainActivityBO(userBargain);

            // 填充额外信息
            fillBargainActivityExtraInfo(activityBO);

            // 如果需要包含助力记录
            if (includeRecords) {
                List<BargainAssistRecordPO> records = bargainAssistRecordMapper.selectByUserBargainId(userBargainId);
                List<BargainAssistRecordBO> recordBOs = BargainBeanConverter.convertToBargainAssistRecordBOList(records);
                activityBO.setAssistRecords(recordBOs);
            }

            return activityBO;

        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询助力活动详情失败", e);
            throw new BizException(MarketingServiceCode.INTERNAL_ERROR, "查询助力活动详情失败: " + e.getMessage(), e);
        }
    }

    @Override
    public BargainAssistCheckResult checkCanAssist(String assistUserId, Long userBargainId, String appId) {
        try {
            // 复用验证逻辑，创建临时DTO对象
            AssistBargainDTO tempDto = new AssistBargainDTO();
            tempDto.setAssistUserId(assistUserId);
            tempDto.setUserBargainId(userBargainId);
            tempDto.setAppId(appId);

            // 调用验证方法，如果没有抛出异常说明可以助力
            validateAndGetBargainActivity(tempDto);
            return BargainAssistCheckResult.success();

        } catch (BizException e) {
            return BargainAssistCheckResult.fail(e.getMessage());
        } catch (Exception e) {
            log.error("检查助力权限失败", e);
            return BargainAssistCheckResult.fail("检查助力权限失败");
        }
    }

    @Override
    public Integer getUserDailyRemainingAssistCount(String userId, String appId) {
        try {
            Integer dailyCount = getUserDailyAssistCount(userId);
            return Math.max(0, DAILY_ASSIST_LIMIT - dailyCount);
        } catch (Exception e) {
            log.error("获取用户每日剩余助力次数失败", e);
            return 0;
        }
    }

    @Override
    public Integer expireBargainActivities() {
        log.info("开始批量过期助力活动");

        try {
            int expiredCount = userBargainActivityMapper.expireActivities(LocalDateTime.now());
            log.info("批量过期助力活动完成，过期数量: {}", expiredCount);
            return expiredCount;
        } catch (Exception e) {
            log.error("批量过期助力活动失败", e);
            return 0;
        }
    }

    /**
     * 获取用户今日助力次数
     */
    private Integer getUserDailyAssistCount(String userId) {
        LocalDate today = LocalDate.now();
        UserDailyAssistLimitPO limitRecord = userDailyAssistLimitMapper.selectByUserAndDate(userId, today);
        return limitRecord != null ? limitRecord.getAssistCount() : 0;
    }

    /**
     * 更新用户每日助力次数
     */
    private void updateUserDailyAssistCount(String userId) {
        LocalDate today = LocalDate.now();
        UserDailyAssistLimitPO limitRecord = userDailyAssistLimitMapper.selectByUserAndDate(userId, today);

        if (limitRecord == null) {
            // 创建新记录
            limitRecord = new UserDailyAssistLimitPO();
            limitRecord.setId(idWorkerCommon.nextId());
            limitRecord.setUserId(userId);
            limitRecord.setAssistDate(today);
            limitRecord.setAssistCount(1);
            limitRecord.setCreateTime(LocalDateTime.now());
            limitRecord.setUpdateTime(LocalDateTime.now());
            userDailyAssistLimitMapper.insertOne(limitRecord);
        } else {
            // 更新计数
            limitRecord.setAssistCount(limitRecord.getAssistCount() + 1);
            limitRecord.setUpdateTime(LocalDateTime.now());
            userDailyAssistLimitMapper.updateById(limitRecord);
        }
    }

    /**
     * 验证权限并获取助力活动信息
     *
     * @param dto 助力请求参数
     * @return 用户助力活动信息
     * @throws BizException 验证失败或活动不存在时抛出异常
     */
    private UserBargainActivityPO validateAndGetBargainActivity(AssistBargainDTO dto) throws BizException {
        // 1. 获取助力活动信息
        UserBargainActivityPO userBargain = userBargainActivityMapper.selectById(dto.getUserBargainId());
        if (userBargain == null) {
            throw new BizException(MarketingServiceCode.NOT_FIND, "助力活动不存在");
        }

        // 2. 检查活动状态
        if (userBargain.getStatus() != 1) {
            throw new BizException(MarketingServiceCode.FAILED_PRECONDITION, "助力活动已结束");
        }

        // 3. 检查是否过期
        if (LocalDateTime.now().isAfter(userBargain.getExpireTime())) {
            throw new BizException(MarketingServiceCode.FAILED_PRECONDITION, "助力活动已过期");
        }

        // 4. 检查是否是自己的活动
        if (dto.getAssistUserId().equals(userBargain.getUserId())) {
            throw new BizException(MarketingServiceCode.FAILED_PRECONDITION, "不能助力自己的活动");
        }

        // 5. 检查是否已经助力过
        BargainAssistRecordPO existingRecord = bargainAssistRecordMapper.selectByUserBargainAndAssistUser(
                dto.getUserBargainId(), dto.getAssistUserId());
        if (existingRecord != null) {
            throw new BizException(MarketingServiceCode.FAILED_PRECONDITION, "已经助力过该活动");
        }

        // 6. 检查每日助力次数限制
        Integer dailyCount = getUserDailyAssistCount(dto.getAssistUserId());
        if (dailyCount >= DAILY_ASSIST_LIMIT) {
            throw new BizException(MarketingServiceCode.FAILED_PRECONDITION, "今日助力次数已达上限");
        }

        return userBargain;
    }

    /**
     * 获取并处理分布式锁
     *
     * @param userBargainId 用户助力活动ID
     * @throws BizException 获取锁失败时抛出异常
     */
    private void acquireDistributedLock(Long userBargainId) throws BizException {
        String lockKey = "bargain_assist_" + userBargainId;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            boolean lockAcquired = lock.tryLock(5, 10, TimeUnit.SECONDS);
            if (!lockAcquired) {
                throw new BizException(MarketingServiceCode.INTERNAL_ERROR, "系统繁忙，请稍后重试");
            }
            log.debug("获取助力活动锁成功: lockKey={}", lockKey);

            // 注册事务同步器，确保锁在事务结束后被释放
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    lock.unlock();
                    log.debug("事务提交后释放锁: lockKey={}", lockKey);
                }

                @Override
                public void afterCompletion(int status) {
                    // 兜底：如果事务回滚了，也要确保锁被释放
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                        log.debug("事务回滚后释放锁: lockKey={}", lockKey);
                    }
                }
            });

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new BizException(MarketingServiceCode.INTERNAL_ERROR, "系统繁忙，请稍后重试", e);
        }
    }

    /**
     * 计算砍价金额 - 使用二倍均值法（微信红包算法）
     *
     * @param userBargain 用户助力活动信息
     * @return 本次砍价金额
     */
    private int calculateBargainAmount(UserBargainActivityPO userBargain) {
        int maxBargainAmount = userBargain.getOriginalPrice() - userBargain.getFloorPrice();
        int currentAssistOrder = userBargain.getAssistCount() + 1;

        // 生成随机种子：基于当前时间戳的纳秒部分，确保每次助力都不同
        // 这样即使同一用户多次助力同一活动，砍价金额也会不同
        long randomSeed = System.nanoTime();

        return BargainAmountCalculatorV2.calculateBargainAmount(
                currentAssistOrder, maxBargainAmount, userBargain.getMinAssistCount(),
                userBargain.getTotalBargainAmount(), randomSeed);
    }

    /**
     * 创建助力记录
     *
     * @param dto 助力请求参数
     * @param userBargain 用户助力活动信息
     * @param bargainAmount 砍价金额
     * @return 创建的助力记录
     * @throws BizException 创建失败时抛出异常
     */
    private BargainAssistRecordPO createAssistRecord(AssistBargainDTO dto, UserBargainActivityPO userBargain, int bargainAmount) throws BizException {
        int currentAssistOrder = userBargain.getAssistCount() + 1;

        BargainAssistRecordPO assistRecord = new BargainAssistRecordPO();
        assistRecord.setId(idWorkerCommon.nextId());
        assistRecord.setUserBargainId(dto.getUserBargainId());
        assistRecord.setAssistUserId(dto.getAssistUserId());
        assistRecord.setInitiatorUserId(userBargain.getUserId());
        assistRecord.setBargainAmount(bargainAmount);
        assistRecord.setAssistOrder(currentAssistOrder);
        assistRecord.setAppId(dto.getAppId());
        assistRecord.setCreateTime(LocalDateTime.now());

        int recordResult = bargainAssistRecordMapper.insertOne(assistRecord);
        if (recordResult <= 0) {
            throw new BizException(MarketingServiceCode.INTERNAL_ERROR, "创建助力记录失败");
        }

        return assistRecord;
    }

    /**
     * 更新助力活动状态
     *
     * @param userBargain 用户助力活动信息
     * @param bargainAmount 本次砍价金额
     * @throws BizException 更新失败时抛出异常
     */
    private void updateBargainActivityStatus(UserBargainActivityPO userBargain, int bargainAmount) throws BizException {
        int currentAssistOrder = userBargain.getAssistCount() + 1;

        // 更新基本信息
        userBargain.setAssistCount(currentAssistOrder);
        userBargain.setTotalBargainAmount(userBargain.getTotalBargainAmount() + bargainAmount);
        userBargain.setCurrentPrice(userBargain.getOriginalPrice() - userBargain.getTotalBargainAmount());
        userBargain.setUpdateTime(LocalDateTime.now());

        // 检查是否达到解锁条件
        int maxBargainAmount = userBargain.getOriginalPrice() - userBargain.getFloorPrice();
        boolean reachedMinCount = currentAssistOrder >= userBargain.getMinAssistCount();
        boolean reachedMinAmount = userBargain.getTotalBargainAmount() >= maxBargainAmount;

        if (reachedMinCount && reachedMinAmount) {
            userBargain.setStatus(2); // 成功，可购买
            userBargain.setSuccessTime(LocalDateTime.now());
        }

        int updateResult = userBargainActivityMapper.updateById(userBargain);
        if (updateResult <= 0) {
            throw new BizException(MarketingServiceCode.INTERNAL_ERROR, "更新助力活动失败");
        }
    }

    /**
     * 填充助力活动额外信息
     */
    private void fillBargainActivityExtraInfo(UserBargainActivityBO activityBO) {
        // 计算是否可以购买
        boolean reachedMinCount = activityBO.getAssistCount() >= activityBO.getMinAssistCount();
        int maxBargainAmount = activityBO.getOriginalPrice() - activityBO.getFloorPrice();
        boolean reachedMinAmount = activityBO.getTotalBargainAmount() >= maxBargainAmount;
        activityBO.setCanPurchase(reachedMinCount && reachedMinAmount);

        // 计算剩余助力人数
        activityBO.setRemainingAssistCount(Math.max(0, activityBO.getMinAssistCount() - activityBO.getAssistCount()));

        // 计算剩余砍价金额
        activityBO.setRemainingBargainAmount(Math.max(0, maxBargainAmount - activityBO.getTotalBargainAmount()));
    }

    /**
     * 生成随机助力人数
     * 在最少助力人数和最大助力人数之间随机生成
     *
     * @param minAssistCount 最少助力人数
     * @param maxAssistCount 最大助力人数
     * @return 随机生成的实际所需助力人数
     */
    private int generateRandomAssistCount(int minAssistCount, int maxAssistCount) {
        if (minAssistCount >= maxAssistCount) {
            return minAssistCount;
        }

        // 使用当前时间戳作为随机种子，确保每次调用都有的随机性
        java.util.Random random = new java.util.Random(System.nanoTime());

        // 在 [minAssistCount, maxAssistCount] 范围内随机生成
        int requiredCount = minAssistCount + random.nextInt(maxAssistCount - minAssistCount + 1);

        log.debug("生成随机助力人数: min={}, max={}, required={}", minAssistCount, maxAssistCount, requiredCount);

        return requiredCount;
    }
}
